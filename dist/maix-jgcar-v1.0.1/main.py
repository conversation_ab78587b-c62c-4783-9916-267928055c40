from maix import image, display, app, time, camera
import cv2
import numpy as np

# 轮廓面积限制变量
MIN_CONTOUR_AREA = 3000   # 最小轮廓面积
MAX_CONTOUR_AREA = 20000 # 最大轮廓面积

# 标志位控制
DRAW_CIRCLE = False   # 是否绘制圆形
DRAW_TRIANGLE = True  # 是否绘制三角形
SHOW_POINT_COORDS = True  # 是否显示点坐标

# 膨胀参数
DO_DILATION = True
DILATION_KERNEL_SIZE = 2
DILATION_ITERATIONS = 1

# 形状参数
TRIANGLE_SCALE = 0.55
TRIANGLE_POS_X = 0.5
TRIANGLE_POS_Y = 0.5
FIXED_SAMPLES = 4  # 每条边的采样点数量

# 点坐标显示参数
FONT_SCALE = 0.4       # 字体大小
FONT_THICKNESS = 1     # 字体粗细
TEXT_COLOR = (0, 255, 0)  # 文本颜色（绿色）
TEXT_MARGIN_LEFT = 10  # 左侧边距
TEXT_SPACING = 15      # 文本行间距
MAX_POINTS_PER_BLOCK = 15  # 每组最多显示的点数
BLOCK_SPACING = 20     # 组间间距

# 初始化显示和相机
disp = display.Display()
cam = camera.Camera(320, 240, image.Format.FMT_BGR888)

print("形状检测程序（带侧边坐标显示）启动...")
print(f"轮廓面积范围: {MIN_CONTOUR_AREA} - {MAX_CONTOUR_AREA}")

frame_count = 0
last_time = time.time()

while not app.need_exit():
    frame_count += 1
    
    # 读取图像
    img = cam.read()
    img_cv = image.image2cv(img, ensure_bgr=False, copy=False)
    img_display = img_cv.copy()
    
    # 图像处理流程
    gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (3, 3), 0)
    edges = cv2.Canny(blurred, 80, 160)
    
    if DO_DILATION:
        kernel = np.ones((DILATION_KERNEL_SIZE, DILATION_KERNEL_SIZE), np.uint8)
        edges = cv2.dilate(edges, kernel, iterations=DILATION_ITERATIONS)
    
    # 查找轮廓
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_TC89_L1)
    
    rectangle_count = 0
    all_points = []  # 存储所有点坐标

    # 轮廓处理
    for contour in contours:
        area = cv2.contourArea(contour)
        if area < MIN_CONTOUR_AREA or area > MAX_CONTOUR_AREA:
            continue
        
        perimeter = cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, 0.03 * perimeter, True)
        
        if len(approx) == 4:  # 只处理四边形
            rectangle_count += 1
            pts = approx.reshape(4, 2).astype(np.float32)
            
            # 顶点排序
            s = pts.sum(axis=1)
            tl, br = pts[np.argmin(s)], pts[np.argmax(s)]
            diff = np.diff(pts, axis=1)
            tr, bl = pts[np.argmin(diff)], pts[np.argmax(diff)]
            
            # 计算中心
            center = (int((tl[0]+br[0])/2), int((tl[1]+br[1])/2))
            
            # 绘制轮廓和中心
            cv2.drawContours(img_display, [approx], -1, (0, 255, 0), 2)
            cv2.circle(img_display, center, 3, (0, 0, 255), -1)
            
            # 仿射变换参数
            target_width = int(np.linalg.norm(tl - tr))
            target_height = int(np.linalg.norm(tl - bl))
            
            if DRAW_TRIANGLE and target_width > 30 and target_height > 30:
                # 计算变换矩阵
                src = np.float32([tl, tr, bl])
                dst = np.float32([[0, 0], [target_width-1, 0], [0, target_height-1]])
                M = cv2.getAffineTransform(src, dst)
                M_inv = cv2.invertAffineTransform(M)
                
                # 三角形参数
                tri_size = int(min(target_width, target_height) * TRIANGLE_SCALE)
                tri_h = int(tri_size * 0.866)
                cx, cy = int(target_width*TRIANGLE_POS_X), int(target_height*TRIANGLE_POS_Y)
                
                # 三角形顶点
                tri_points = np.array([
                    [cx, cy - tri_h//2],
                    [cx - tri_size//2, cy + tri_h//2],
                    [cx + tri_size//2, cy + tri_h//2]
                ], np.float32)
                
                # 生成边上的点
                dense_pts = []
                for i in range(3):
                    p1, p2 = tri_points[i], tri_points[(i+1)%3]
                    for j in range(FIXED_SAMPLES + 1):
                        t = j / FIXED_SAMPLES
                        x = p1[0] + t*(p2[0]-p1[0])
                        y = p1[1] + t*(p2[1]-p1[1])
                        dense_pts.append([x, y])
                
                # 映射回原图
                dense_pts = cv2.transform(np.array([dense_pts], np.float32), M_inv)[0]
                pts_int = dense_pts.astype(np.int32)
                
                # 绘制点和连接线
                cv2.polylines(img_display, [pts_int], True, (0, 165, 255), 2)
                for p in pts_int:
                    cv2.circle(img_display, (p[0], p[1]), 2, (0, 255, 255), -1)
                
                # 存储点坐标
                all_points.extend([(p[0], p[1]) for p in pts_int])

    # 在左侧显示所有点的坐标
    if SHOW_POINT_COORDS and all_points:
        y_pos = 40  # 起始Y位置（在统计信息下方）
        block_count = 0
        
        for i, (x, y) in enumerate(all_points):
            # 每MAX_POINTS_PER_BLOCK个点换一组显示
            if i > 0 and i % MAX_POINTS_PER_BLOCK == 0:
                y_pos += BLOCK_SPACING
                block_count += 1
            
            # 确保不超出图像范围
            if y_pos + TEXT_SPACING > img_display.shape[0] - 10:
                break
            
            # 显示坐标文本
            text = f"point{i+1}: ({x}, {y})"
            cv2.putText(img_display, text, 
                      (TEXT_MARGIN_LEFT, y_pos + TEXT_SPACING * block_count), 
                      cv2.FONT_HERSHEY_SIMPLEX, 
                      FONT_SCALE, TEXT_COLOR, FONT_THICKNESS)
            y_pos += TEXT_SPACING

    # 显示统计信息
    current_time = time.time()
    fps = int(frame_count / (current_time - last_time)) if (current_time - last_time) > 0 else 0
    cv2.putText(img_display, f"FPS: {fps} | Rect: {rectangle_count} | Points: {len(all_points)}", 
               (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # 显示图像
    img_show = image.cv2image(img_display, bgr=True, copy=False)
    disp.show(img_show)