# camera_adapter.py - 摄像头适配器
# 将OpenCV的VideoCapture封装成与maix.camera兼容的接口

import cv2
import numpy as np
import time

class CameraAdapter:
    """摄像头适配器类 - 兼容maix.camera接口"""
    
    def __init__(self, width, height, format_type=None):
        """初始化摄像头适配器
        
        Args:
            width (int): 图像宽度
            height (int): 图像高度  
            format_type: 图像格式(兼容参数，实际使用BGR格式)
        """
        self.width = width
        self.height = height
        self.format_type = format_type
        self.cap = None
        self.is_opened = False
        
        # 尝试初始化摄像头
        self._initialize_camera()
        
    def _initialize_camera(self):
        """初始化摄像头设备"""
        # 尝试多个摄像头设备索引
        camera_indices = [0, 1, 2, '/dev/video0', '/dev/video1']
        
        for idx in camera_indices:
            try:
                print(f"尝试打开摄像头设备: {idx}")
                self.cap = cv2.VideoCapture(idx)
                
                if self.cap.isOpened():
                    # 设置分辨率
                    self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.width)
                    self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.height)
                    
                    # 设置其他参数
                    self.cap.set(cv2.CAP_PROP_FPS, 30)  # 设置帧率
                    self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 减少缓冲延迟
                    
                    # 验证设置是否生效
                    actual_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    actual_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    actual_fps = self.cap.get(cv2.CAP_PROP_FPS)
                    
                    print(f"摄像头初始化成功:")
                    print(f"  设备: {idx}")
                    print(f"  分辨率: {actual_width}x{actual_height} (目标: {self.width}x{self.height})")
                    print(f"  帧率: {actual_fps} FPS")
                    
                    # 测试读取一帧
                    ret, test_frame = self.cap.read()
                    if ret and test_frame is not None:
                        print(f"  测试帧: {test_frame.shape}")
                        self.is_opened = True
                        return
                    else:
                        print(f"  测试读取失败")
                        self.cap.release()
                        continue
                else:
                    print(f"  无法打开设备 {idx}")
                    continue
                    
            except Exception as e:
                print(f"  初始化设备 {idx} 时出错: {e}")
                if self.cap:
                    self.cap.release()
                continue
        
        # 如果所有设备都失败
        print("错误: 无法找到可用的摄像头设备")
        print("请检查:")
        print("1. USB摄像头是否正确连接")
        print("2. 设备权限: sudo chmod 666 /dev/video*")
        print("3. 是否有其他程序占用摄像头")
        self.is_opened = False
        
    def read(self):
        """读取一帧图像
        
        Returns:
            numpy.ndarray or None: BGR格式的图像数组，失败时返回None
        """
        if not self.is_opened or not self.cap:
            print("警告: 摄像头未正确初始化")
            return None
            
        try:
            ret, frame = self.cap.read()
            if ret and frame is not None:
                # 确保图像是BGR格式(OpenCV默认)
                if len(frame.shape) == 3 and frame.shape[2] == 3:
                    return frame
                else:
                    print(f"警告: 图像格式异常 {frame.shape}")
                    return None
            else:
                print("警告: 读取图像失败")
                return None
                
        except Exception as e:
            print(f"读取图像时出错: {e}")
            return None
    
    def close(self):
        """关闭摄像头"""
        if self.cap:
            self.cap.release()
            print("摄像头已关闭")
        self.is_opened = False
        
    def is_available(self):
        """检查摄像头是否可用
        
        Returns:
            bool: 摄像头是否可用
        """
        return self.is_opened and self.cap and self.cap.isOpened()
    
    def get_resolution(self):
        """获取当前分辨率
        
        Returns:
            tuple: (width, height)
        """
        if self.is_available():
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            return (width, height)
        return (0, 0)
    
    def set_resolution(self, width, height):
        """设置分辨率
        
        Args:
            width (int): 新的宽度
            height (int): 新的高度
            
        Returns:
            bool: 设置是否成功
        """
        if not self.is_available():
            return False
            
        try:
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
            
            # 验证设置
            actual_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            actual_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            self.width = actual_width
            self.height = actual_height
            
            print(f"分辨率已更新为: {actual_width}x{actual_height}")
            return True
            
        except Exception as e:
            print(f"设置分辨率时出错: {e}")
            return False
    
    def __del__(self):
        """析构函数，确保资源释放"""
        self.close()


# 兼容性类型定义(模拟maix.image.Format)
class Format:
    """图像格式定义 - 兼容maix.image.Format"""
    FMT_BGR888 = "BGR888"
    FMT_RGB888 = "RGB888"
    FMT_GRAY = "GRAY"


# 工厂函数，兼容原有调用方式
def Camera(width, height, format_type=Format.FMT_BGR888):
    """创建摄像头实例 - 兼容maix.camera.Camera调用方式
    
    Args:
        width (int): 图像宽度
        height (int): 图像高度
        format_type: 图像格式
        
    Returns:
        CameraAdapter: 摄像头适配器实例
    """
    return CameraAdapter(width, height, format_type)
