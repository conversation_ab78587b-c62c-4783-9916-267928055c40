# purple_laser_detector.py - 紫色激光检测器模块
# 从MaixCAM项目移植，保持算法完全不变

import cv2
import numpy as np

class PurpleLaserDetector:
    """紫色激光检测器类 - 完全移植自MaixCAM版本"""
    
    def __init__(self, pixel_radius=3):
        self.pixel_radius = pixel_radius
        self.kernel = np.ones((3, 3), np.uint8)
        
        # 紫色HSV范围参数
        self.lower_purple = np.array([54, 30, 56])
        self.upper_purple = np.array([230, 178, 255])
        
        # 激光点筛选参数
        self.min_area = 5
        self.max_area = 200
        self.min_circularity = 0.3
        self.min_brightness_v = 150
        
    def detect(self, img):
        """检测紫色激光点
        
        Args:
            img: 输入图像(BGR格式)
            
        Returns:
            tuple: (处理后的图像, 激光点列表)
        """
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        
        # 1. 紫色HSV范围检测
        mask_purple = cv2.inRange(hsv, self.lower_purple, self.upper_purple)
        
        # 2. 形态学操作，去除噪点并连接可能的激光点区域
        mask_purple = cv2.morphologyEx(mask_purple, cv2.MORPH_OPEN, self.kernel)  # 先开运算去噪
        mask_purple = cv2.morphologyEx(mask_purple, cv2.MORPH_CLOSE, self.kernel) # 再闭运算连接
        
        # 3. 查找轮廓
        contours_purple, _ = cv2.findContours(mask_purple, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        best_laser_point = None
        best_score = -1 # 用于选择最佳点的分数
        
        # 4. 筛选候选激光点
        for cnt in contours_purple:
            area = cv2.contourArea(cnt)
            
            # a. 面积筛选：排除过小或过大的区域
            if not (self.min_area <= area <= self.max_area):
                continue
                
            # b. 圆形度筛选
            perimeter = cv2.arcLength(cnt, True)
            if perimeter == 0: continue # 避免除以零
            circularity = 4 * np.pi * area / (perimeter * perimeter)
            if circularity < self.min_circularity:
                continue
                
            # c. 计算轮廓中心作为激光点坐标
            rect = cv2.minAreaRect(cnt)
            cx, cy = map(int, rect[0])
            
            # d. 亮度筛选
            v_value_at_center = hsv[cy, cx, 2] # 获取中心点的V值
            if v_value_at_center < self.min_brightness_v:
                continue
                
            # e. 计算评分
            score = v_value_at_center
            # f. 选择最佳点
            if score > best_score:
                best_score = score
                best_laser_point = (cx, cy)
        
        # 5. 如果找到最佳激光点，则绘制并返回
        laser_points = []
        if best_laser_point is not None:
            cx, cy = best_laser_point
            # 绘制激光点标记
            cv2.circle(img, (cx, cy), self.pixel_radius, (255, 0, 255), -1)
            cv2.putText(img, "Purple", (cx - 20, cy - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)
            laser_points = [best_laser_point]
            
        return img, laser_points
    
    def update_parameters(self, **kwargs):
        """更新检测参数
        
        Args:
            **kwargs: 参数字典，支持的参数包括：
                - min_area: 最小面积
                - max_area: 最大面积
                - min_circularity: 最小圆形度
                - min_brightness_v: 最小亮度值
                - lower_purple: HSV下限
                - upper_purple: HSV上限
        """
        for key, value in kwargs.items():
            if hasattr(self, key):
                if key in ['lower_purple', 'upper_purple']:
                    setattr(self, key, np.array(value))
                else:
                    setattr(self, key, value)
                print(f"Updated {key} = {value}")
            else:
                print(f"Warning: Unknown parameter {key}")
