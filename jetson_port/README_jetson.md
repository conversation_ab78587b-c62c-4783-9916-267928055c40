# Jetson Orin Nano 视觉处理程序

## 项目简介

本项目是将基于MaixCAM的视觉处理程序移植到Jetson Orin Nano平台的版本。保持了原有的紫色激光检测、矩形检测和串口通信功能，同时适配了USB摄像头和全屏显示。

## 主要功能

- **紫色激光检测**: 基于HSV颜色空间的激光点识别和跟踪
- **矩形检测**: 四边形目标检测和中心点计算
- **串口通信**: 实时发送检测到的坐标数据
- **全屏显示**: 创建幕布显示图像，支持1920x1080分辨率

## 技术特性

- **硬件适配**: 采用适配器模式，最小化代码修改
- **高分辨率支持**: 从320x240升级到1920x1080
- **参数自适应**: 基于分辨率比例自动调整检测参数
- **性能优化**: 支持图像缩放和GPU加速选项

## 系统要求

- **硬件**: Jetson Orin Nano开发板
- **摄像头**: USB摄像头，支持1920x1080分辨率
- **串口**: USB转串口适配器或板载串口
- **显示**: HDMI显示器(支持全屏显示)

## 安装指南

### 1. 环境准备

```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 安装Python开发环境
sudo apt install python3-pip python3-dev -y

# 安装系统依赖
sudo apt install libopencv-dev python3-opencv -y
```

### 2. 安装Python依赖

```bash
cd jetson_port
pip3 install -r requirements.txt
```

### 3. 硬件连接

- 连接USB摄像头到Jetson开发板
- 连接串口设备(通常为/dev/ttyUSB0或/dev/ttyACM0)
- 连接HDMI显示器

## 使用方法

### 基本运行

```bash
cd jetson_port
python3 main_jetson.py
```

### 配置选项

程序支持多种配置选项，可在`config/jetson_config.py`中调整：

- **分辨率设置**: 支持1920x1080和960x540两种模式
- **检测参数**: 轮廓面积阈值、激光检测参数等
- **性能选项**: 图像缩放、帧率控制等

### 串口配置

默认串口设置：
- 设备: `/dev/ttyUSB0`
- 波特率: `115200`
- 数据格式: `8N1`

可在配置文件中修改串口参数以适配不同硬件。

## 项目结构

```
jetson_port/
├── adapters/                    # 硬件适配层
│   ├── camera_adapter.py        # 摄像头适配器
│   ├── display_adapter.py       # 显示适配器
│   ├── app_adapter.py          # 应用控制适配器
│   └── time_adapter.py         # 时间功能适配器
├── micu_uart_lib_jetson/       # 串口通信库(移植版)
│   ├── simple_uart.py          # 串口管理
│   ├── utils.py               # 工具函数
│   └── config.py              # 配置管理
├── config/                     # 配置文件
│   └── jetson_config.py        # Jetson平台配置
├── main_jetson.py             # 主程序
├── requirements.txt           # 依赖管理
└── README_jetson.md          # 项目说明
```

## 数据格式

### 串口输出格式

- **目标中心点**: `to:(x,y)` - 检测到的矩形中心坐标
- **激光点**: `pur:(x,y)` - 紫色激光点坐标

坐标系统：左下角为原点(0,0)，向右为X正方向，向上为Y正方向。

## 性能优化

### GPU加速

如需启用GPU加速，请安装支持CUDA的OpenCV版本：

```bash
pip3 install opencv-contrib-python
```

### 分辨率优化

对于性能要求较高的场景，可使用960x540分辨率模式：

```python
# 在jetson_config.py中设置
current_resolution = '960x540'
```

## 故障排除

### 常见问题

1. **摄像头无法打开**
   - 检查USB连接
   - 确认摄像头权限: `sudo chmod 666 /dev/video*`

2. **串口连接失败**
   - 检查串口设备路径: `ls /dev/tty*`
   - 确认串口权限: `sudo usermod -a -G dialout $USER`

3. **显示窗口无法全屏**
   - 检查显示器分辨率设置
   - 尝试窗口模式: 在display_adapter.py中设置fullscreen=False

### 调试模式

启用详细日志输出：

```bash
export JETSON_DEBUG=1
python3 main_jetson.py
```

## 开发说明

### 移植策略

本项目采用**最小侵入式适配**策略：

1. **保持算法不变**: PurpleLaserDetector类和检测算法完全保持原样
2. **硬件抽象层**: 通过适配器模式封装硬件差异
3. **配置驱动**: 通过配置文件管理平台差异
4. **渐进式验证**: 分模块测试，确保每个组件独立可用

### 扩展开发

如需添加新功能或适配其他硬件平台，建议：

1. 在`adapters/`目录下创建新的适配器
2. 在`config/`目录下添加平台特定配置
3. 保持与现有接口的兼容性

## 版本历史

- **v1.0.0**: 初始移植版本，支持基本功能
- **v1.0.1**: 添加性能优化和GPU加速支持
- **v1.0.2**: 完善文档和故障排除指南

## 许可证

本项目基于原MaixCAM项目进行移植开发，遵循相同的开源许可证。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目仓库: [GitHub链接]
- 技术支持: [邮箱地址]

---

**注意**: 本文档会随着项目开发进度持续更新，请关注最新版本。
